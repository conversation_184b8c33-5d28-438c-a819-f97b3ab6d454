const express = require('express');
const router = express.Router();
const requestController = require('../controllers/requestController');
const auth = require('../middleware/auth');

router.post('/create', auth, requestController.createRequest);
router.get('/nearby', auth, requestController.getNearbyRequests);
router.get('/by-blood-type', auth, requestController.getRequestsByBloodType);
router.get('/', auth, requestController.getAllRequests);
router.patch('/fulfill/:id', auth, requestController.fulfillRequest);

// Hospital-specific routes
router.get('/hospital', auth, requestController.getHospitalRequests);
router.get('/:id/matched-donors', auth, requestController.getMatchedDonors);

module.exports = router;
