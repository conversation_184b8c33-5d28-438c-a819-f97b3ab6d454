import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Auth API calls
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  getProfile: () => api.get('/auth/profile'),
  updateProfile: (data) => api.put('/auth/profile', data)
};

// Blood Request API calls
export const requestAPI = {
  createRequest: (requestData) => api.post('/requests/create', requestData),
  getNearbyRequests: (location) => api.get('/requests/nearby', { params: location }),
  getAllRequests: () => api.get('/requests'),
  getRequestsByBloodType: (bloodType) => api.get('/requests/by-blood-type', { params: { bloodType } }),
  fulfillRequest: (requestId) => api.patch(`/requests/fulfill/${requestId}`),
  getHospitalRequests: () => api.get('/requests/hospital'),
  getMatchedDonors: (requestId) => api.get(`/requests/${requestId}/matched-donors`)
};

// Donor API calls
export const donorAPI = {
  getDonationHistory: () => api.get('/donor/history'),
  getDonorStats: () => api.get('/donor/stats'),
  respondToRequest: (requestId) => api.post(`/donor/respond/${requestId}`)
};

// Notification API calls
export const notificationAPI = {
  getNotifications: () => api.get('/notifications'),
  markAsRead: (notificationId) => api.patch(`/notifications/${notificationId}/read`),
  markAllAsRead: () => api.patch('/notifications/read-all')
};

// Blood Inventory API calls
export const inventoryAPI = {
  getInventory: () => api.get('/inventory'),
  updateInventory: (data) => api.put('/inventory', data),
  getExpiringBlood: () => api.get('/inventory/expiring')
};

// Admin API calls
export const adminAPI = {
  getAdminDashboard: () => api.get('/admin/dashboard'),
  getAllUsers: (params) => api.get('/admin/users', { params }),
  getAllHospitals: (params) => api.get('/admin/hospitals', { params }),
  getAllRequests: (params) => api.get('/admin/requests', { params }),
  getAnalytics: () => api.get('/admin/analytics'),
  verifyHospital: (hospitalId, action) => api.patch(`/admin/verify-hospital/${hospitalId}`, { action }),
  banUser: (userId, action, reason) => api.patch(`/admin/ban-user/${userId}`, { action, reason }),
  deleteUser: (userId) => api.delete(`/admin/users/${userId}`),
  // Add other admin-specific calls as needed
};

// Alert API calls
export const alertAPI = {
  sendEmergencyAlert: (alertData) => api.post('/alerts/emergency', alertData),
};

export default api;
