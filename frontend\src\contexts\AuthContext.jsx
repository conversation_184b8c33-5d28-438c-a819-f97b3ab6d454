import { createContext, useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import axios from 'axios';

// Configure axios base URL
axios.defaults.baseURL = 'http://localhost:3001';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token'));
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // Configure axios defaults
  useEffect(() => {
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete axios.defaults.headers.common['Authorization'];
    }
  }, [token]);

  // Check if user is logged in on app start
  useEffect(() => {
    const checkAuth = async () => {
      if (token) {
        try {
          // Decode token to get user info (simple JWT decode)
          const payload = JSON.parse(atob(token.split('.')[1]));
          setUser({
            id: payload.id,
            role: payload.role,
            name: payload.name
          });
        } catch (error) {
          console.error('Invalid token:', error);
          logout();
        }
      }
      setLoading(false);
    };

    checkAuth();
  }, [token]);

  const login = async (email, password) => {
    try {
      const response = await axios.post('http://localhost:3001/api/auth/login', {
        email,
        password
      });

      if (response.data.success) {
        const { token } = response.data;
        setToken(token);
        localStorage.setItem('token', token);

        // Decode token to get user info
        const payload = JSON.parse(atob(token.split('.')[1]));
        setUser({
          id: payload.id,
          role: payload.role,
          name: payload.name
        });

        toast.success('Login successful!');

        // Redirect based on role to dedicated dashboards
        if (payload.role === 'admin') {
          navigate('/admin');
        } else if (payload.role === 'hospital') {
          navigate('/hospital');
        } else if (payload.role === 'donor') {
          navigate('/donor');
        } else {
          // Fallback to general dashboard for unknown roles
          navigate('/dashboard');
        }

        return { success: true };
      }
    } catch (error) {
      console.error('Login error:', error);
      const message = error.response?.data?.message || 'Login failed';
      toast.error(message);
      return {
        success: false,
        message: message
      };
    }
  };

  const register = async (userData, userType = 'donor') => {
    try {
      const endpoint = userType === 'hospital'
        ? 'http://localhost:3001/api/auth/register-hospital'
        : 'http://localhost:3001/api/auth/register-donor';

      const response = await axios.post(endpoint, userData);

      if (response.data.success) {
        const { token } = response.data;
        setToken(token);
        localStorage.setItem('token', token);

        // Decode token to get user info
        const payload = JSON.parse(atob(token.split('.')[1]));
        setUser({
          id: payload.id,
          role: payload.role || userType,
          name: payload.name
        });

        toast.success('Registration successful!');

        // Redirect based on role to dedicated dashboards
        const role = payload.role || userType;
        if (role === 'admin') {
          navigate('/admin');
        } else if (role === 'hospital') {
          navigate('/hospital');
        } else if (role === 'donor') {
          navigate('/donor');
        } else {
          // Fallback to general dashboard for unknown roles
          navigate('/dashboard');
        }

        return { success: true };
      }
    } catch (error) {
      console.error('Registration error:', error);
      const message = error.response?.data?.message || 'Registration failed';
      toast.error(message);
      return {
        success: false,
        message: message
      };
    }
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('token');
    delete axios.defaults.headers.common['Authorization'];
  };

  const value = {
    user,
    token,
    loading,
    login,
    register,
    logout,
    isAuthenticated: !!user,
    isAdmin: user?.role === 'admin',
    isHospital: user?.role === 'hospital',
    isDonor: user?.role === 'donor'
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
