const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const BloodRequest = require('../models/BloodRequest');
const User = require('../models/User');

// Middleware to check if user is a donor
const donorAuth = async (req, res, next) => {
  try {
    if (req.user.role !== 'donor') {
      return res.status(403).json({ message: 'Access denied. Donor privileges required.' });
    }
    next();
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};

// GET /api/donor/history - Get donation history and responses
router.get('/history', auth, donorAuth, async (req, res) => {
  try {
    // Find all requests where this donor has responded (either fulfilled or in matchedDonors)
    const responses = await BloodRequest.find({
      $or: [
        { fulfilledBy: req.user.id },
        { matchedDonors: req.user.id }
      ]
    })
    .populate('hospital', 'name location')
    .sort({ updatedAt: -1 });

    const formattedResponses = responses.map(response => ({
      id: response._id,
      date: response.fulfilledBy?.toString() === req.user.id.toString()
        ? response.fulfilledAt
        : response.updatedAt,
      hospital: response.hospital?.name || 'Unknown Hospital',
      status: response.status,
      units: response.units,
      bloodType: response.bloodType,
      location: response.hospital?.location || response.location
    }));

    res.json({
      success: true,
      donations: formattedResponses
    });
  } catch (error) {
    console.error('Error fetching donation history:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/donor/stats - Get donor statistics
router.get('/stats', auth, donorAuth, async (req, res) => {
  try {
    const donorId = req.user.id;

    // Calculate donor statistics
    const totalDonations = await BloodRequest.countDocuments({
      fulfilledBy: donorId,
      status: 'fulfilled'
    });

    // Estimate lives saved (typically 1 donation can save up to 3 lives)
    const livesSaved = totalDonations * 3;

    // Get donor's blood type from user profile
    const donor = await User.findById(donorId).select('bloodType lastDonation');

    // Calculate achievements based on donation count
    const achievements = [];
    if (totalDonations >= 1) achievements.push('First Donation');
    if (totalDonations >= 5) achievements.push('Life Saver');
    if (totalDonations >= 10) achievements.push('Regular Donor');
    if (totalDonations >= 25) achievements.push('Hero Donor');
    if (totalDonations >= 50) achievements.push('Super Hero');

    res.json({
      success: true,
      stats: {
        totalDonations,
        livesSaved,
        bloodType: donor?.bloodType || 'Not specified',
        lastDonation: donor?.lastDonation,
        achievements
      }
    });
  } catch (error) {
    console.error('Error fetching donor stats:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/donor/respond/:requestId - Respond to a blood request
router.post('/respond/:requestId', auth, donorAuth, async (req, res) => {
  try {
    const requestId = req.params.requestId;
    const donorId = req.user.id;

    const request = await BloodRequest.findById(requestId);
    if (!request) {
      return res.status(404).json({ success: false, message: 'Request not found' });
    }

    if (request.status !== 'pending') {
      return res.status(400).json({ success: false, message: 'Request is no longer active' });
    }

    // Add donor to matched donors if not already there
    if (!request.matchedDonors.includes(donorId)) {
      request.matchedDonors.push(donorId);
      await request.save();
    }

    res.json({
      success: true,
      message: 'Response recorded successfully',
      request
    });
  } catch (error) {
    console.error('Error responding to request:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
