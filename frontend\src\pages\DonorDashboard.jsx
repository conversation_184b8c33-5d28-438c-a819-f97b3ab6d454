import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { authAPI, donorAPI, requestAPI } from '../services/api';
import { toast } from 'react-hot-toast';
import {
  Heart,
  MapPin,
  Calendar,
  Clock,
  AlertCircle,
  Award,
  Phone,
  User,
  Activity,
  Target,
  Star,
  Navigation,
  Bell,
  TrendingUp,
  Loader2,
  Menu,
  X,
  LogOut,
  FileText,
  Home,
  ChevronRight,
  Settings
} from 'lucide-react';

const DonorDashboard = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [nearbyRequests, setNearbyRequests] = useState([]);
  const [allBloodTypeRequests, setAllBloodTypeRequests] = useState([]);
  const [donationHistory, setDonationHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [nextEligibleDate, setNextEligibleDate] = useState(null);
  const [showAllRequests, setShowAllRequests] = useState(false);
  const [showEditProfile, setShowEditProfile] = useState(false);
  const [editProfileData, setEditProfileData] = useState({
    name: '',
    email: '',
    phone: '',
    bloodType: '',
    location: { address: '', coordinates: { latitude: 0, longitude: 0 } }
  });
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      sms: false,
      push: true
    },
    privacy: {
      showLocation: true,
      showLastDonation: true
    },
    preferences: {
      maxDistance: 50,
      urgencyLevels: ['high', 'medium', 'low']
    }
  });

  const [donorProfile, setDonorProfile] = useState({
    name: user?.name || '',
    bloodType: '',
    location: null, // Should be an object { address, coordinates: { latitude, longitude } }
    phone: '',
    lastDonation: null,
    totalDonations: 0,
    livesSaved: 0,
    achievements: []
  });

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch donor profile
        const profileResponse = await authAPI.getProfile();
        if (profileResponse.data.success && profileResponse.data.user) {
          setDonorProfile(prev => ({
            ...prev,
            ...profileResponse.data.user,
            name: profileResponse.data.user.name // Ensure name is taken from fetched profile
          }));

          // Fetch donor stats
          const statsResponse = await donorAPI.getDonorStats();
          if (statsResponse.data.success) {
            setDonorProfile(prev => ({
              ...prev,
              totalDonations: statsResponse.data.stats.totalDonations,
              livesSaved: statsResponse.data.stats.livesSaved,
              achievements: statsResponse.data.stats.achievements,
              lastDonation: statsResponse.data.stats.lastDonation
            }));
          } else {
            toast.error('Failed to fetch donor stats');
          }

          // Fetch donation history
          const historyResponse = await donorAPI.getDonationHistory();
          if (historyResponse.data.success) {
            setDonationHistory(historyResponse.data.donations || []);
          } else {
            toast.error('Failed to fetch donation history');
            setDonationHistory([]);
          }

          // Fetch nearby requests (only if location and blood type are available)
          if (profileResponse.data.user?.location?.coordinates && profileResponse.data.user?.bloodType) {
            const requestsResponse = await requestAPI.getNearbyRequests({
              latitude: profileResponse.data.user.location.coordinates.latitude,
              longitude: profileResponse.data.user.location.coordinates.longitude
            });

            if (requestsResponse.data.success) {
              setNearbyRequests(requestsResponse.data.requests.map(request => ({
                id: request._id,
                hospitalName: request.hospital?.name || 'Unknown Hospital',
                bloodType: request.bloodType,
                units: request.units,
                urgency: request.urgency,
                location: request.location?.address || 'Unknown Location',
                distance: 'Calculating...', // TODO: Calculate distance on frontend or backend
                timePosted: new Date(request.createdAt).toLocaleString(),
              })) || []);
            } else {
              toast.error('Failed to fetch nearby requests');
              setNearbyRequests([]);
            }
          } else {
            console.log('Skipping nearby requests fetch: Donor location or blood type not available.');
            setNearbyRequests([]);
          }

          // Fetch all blood type matching requests automatically
          if (profileResponse.data.user?.bloodType) {
            try {
              const bloodTypeResponse = await requestAPI.getRequestsByBloodType(profileResponse.data.user.bloodType);

              if (bloodTypeResponse.data.success) {
                setAllBloodTypeRequests(bloodTypeResponse.data.requests.map(request => ({
                  id: request._id,
                  hospitalName: request.hospital?.name || 'Unknown Hospital',
                  bloodType: request.bloodType,
                  units: request.units,
                  urgency: request.urgency,
                  location: request.location?.address || 'Unknown Location',
                  timePosted: new Date(request.createdAt).toLocaleString(),
                  status: request.status || 'active'
                })) || []);
              }
            } catch (error) {
              console.error('Error fetching blood type requests:', error);
            }
          }

        } else {
          toast.error('Failed to fetch donor profile');
        }
      } catch (error) {
        console.error('Error fetching donor dashboard data:', error);
        toast.error('Error loading dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user]); // Dependency on user to refetch if user context changes

  // Calculate next eligible donation date when lastDonation changes
  useEffect(() => {
    if (donorProfile.lastDonation) {
      const lastDate = new Date(donorProfile.lastDonation);
      const nextDate = new Date(lastDate);
      nextDate.setDate(lastDate.getDate() + 56); // 8 weeks between donations

      const today = new Date();
      const daysUntilEligible = Math.ceil((nextDate - today) / (1000 * 60 * 60 * 24));

      setNextEligibleDate({
        date: nextDate,
        daysRemaining: daysUntilEligible > 0 ? daysUntilEligible : 0,
        canDonate: daysUntilEligible <= 0
      });
    } else {
      setNextEligibleDate(null); // Reset if no last donation date
    }
  }, [donorProfile.lastDonation]);

  const getUrgencyColor = (urgency) => {
    switch (urgency) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleNavigateToRequests = async () => {
    if (!donorProfile.bloodType) {
      toast.error('Please update your blood type in your profile first');
      return;
    }

    try {
      setLoading(true);
      const response = await requestAPI.getRequestsByBloodType(donorProfile.bloodType);

      if (response.data.success) {
        setAllBloodTypeRequests(response.data.requests.map(request => ({
          id: request._id,
          hospitalName: request.hospital?.name || 'Unknown Hospital',
          bloodType: request.bloodType,
          units: request.units,
          urgency: request.urgency,
          location: request.location?.address || 'Unknown Location',
          timePosted: new Date(request.createdAt).toLocaleString(),
          status: request.status || 'active'
        })) || []);
        setShowAllRequests(true);
      } else {
        toast.error('Failed to fetch blood requests');
      }
    } catch (error) {
      console.error('Error fetching blood type requests:', error);
      toast.error('Error loading blood requests');
    } finally {
      setLoading(false);
    }
  };

  const handleRespondToRequest = async (requestId) => {
    try {
      // Use the donor respond endpoint
      const response = await donorAPI.respondToRequest(requestId);
      if (response.data.success) {
        toast.success('Successfully responded to request!');

        // Update the nearby requests to remove the responded request
        setNearbyRequests(prevRequests => prevRequests.filter(req => req.id !== requestId));
        setAllBloodTypeRequests(prevRequests => prevRequests.filter(req => req.id !== requestId));

        // Refresh donation history to show the new response
        try {
          const historyResponse = await donorAPI.getDonationHistory();
          if (historyResponse.data.success) {
            setDonationHistory(historyResponse.data.donations || []);
          }
        } catch (historyError) {
          console.error('Error refreshing donation history:', historyError);
        }

      } else {
        toast.error(response.data.message || 'Failed to respond to request.');
      }
    } catch (error) {
      console.error('Error responding to request:', error);
      toast.error('Failed to respond to request.');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  // Check if user data is loaded but bloodType or location is missing for requests
  const showProfileWarning = !donorProfile.bloodType || !donorProfile.location;

  // Sidebar menu items
  const sidebarItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Home, active: true },
    { id: 'requests', label: 'Blood Requests', icon: Activity },
    { id: 'history', label: 'My Responses', icon: Calendar },
    { id: 'achievements', label: 'Achievements', icon: Award },
    { id: 'settings', label: 'Settings', icon: Settings },
    { id: 'profile', label: 'Profile', icon: User }
  ];

  return (
    <div className="min-h-screen bg-gray-100 flex">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-64' : 'w-20'} bg-white shadow-xl transition-all duration-300 ease-in-out flex flex-col`}>
        {/* Sidebar Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="bg-red-600 p-2 rounded-lg">
              <Heart className="h-6 w-6 text-white" />
            </div>
            {sidebarOpen && (
              <div>
                <h2 className="text-lg font-bold text-gray-800">🩸 SomDonate</h2>
                <p className="text-sm text-gray-600">Donor Panel</p>
              </div>
            )}
          </div>
        </div>

        {/* Sidebar Menu */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {sidebarItems.map((item) => (
              <li key={item.id}>
                <button
                  onClick={() => setActiveTab(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                    activeTab === item.id
                      ? 'bg-red-50 text-red-600 border-r-4 border-red-600'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-red-600'
                  }`}
                >
                  <item.icon className="h-5 w-5" />
                  {sidebarOpen && <span className="font-medium">{item.label}</span>}
                  {sidebarOpen && activeTab === item.id && (
                    <ChevronRight className="h-4 w-4 ml-auto" />
                  )}
                </button>
              </li>
            ))}
          </ul>
        </nav>

        {/* Sidebar Footer */}
        <div className="p-4 border-t border-gray-200">
          <button
            onClick={logout}
            className="w-full flex items-center space-x-3 px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
          >
            <LogOut className="h-5 w-5" />
            {sidebarOpen && <span className="font-medium">Logout</span>}
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <Menu className="h-5 w-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">
                  {sidebarItems.find(item => item.id === activeTab)?.label || 'Dashboard'}
                </h1>
                <p className="text-sm text-gray-600">Welcome back, {user?.name}</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <button className="relative p-2 rounded-lg hover:bg-gray-100 transition-colors">
                <Bell className="h-5 w-5 text-gray-600" />
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {nearbyRequests.length}
                </span>
              </button>

              {/* Profile */}
              <div className="flex items-center space-x-3">
                <div className="bg-red-600 p-2 rounded-full">
                  <Heart className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-800">{user?.name}</p>
                  <p className="text-xs text-gray-600">Donor</p>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 p-6 bg-gray-50 overflow-auto">
          {/* Dashboard Content */}
          {activeTab === 'dashboard' && (
            <div className="space-y-6">
              {showProfileWarning && (
                <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4" role="alert">
                  <p className="font-bold">Incomplete Profile</p>
                  <p>Please update your <a href="/profile" className="text-yellow-800 underline">profile</a> with your blood type and location to see personalized requests.</p>
                </div>
              )}

              {/* Donor Profile Card */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center space-x-2">
                  <User className="h-5 w-5 text-blue-500" />
                  Your Profile
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="flex items-center space-x-3">
                    <Heart className="h-6 w-6 text-red-500" />
                    <div>
                      <p className="text-gray-500 text-sm">Blood Type</p>
                      <p className="font-medium text-gray-800">{donorProfile.bloodType || 'Not specified'}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <MapPin className="h-6 w-6 text-blue-500" />
                    <div>
                      <p className="text-gray-500 text-sm">Location</p>
                      <p className="font-medium text-gray-800">{donorProfile.location?.address || 'Not specified'}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="h-6 w-6 text-green-500" />
                    <div>
                      <p className="text-gray-500 text-sm">Phone</p>
                      <p className="font-medium text-gray-800">{donorProfile.phone || 'Not specified'}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-6 w-6 text-purple-500" />
                    <div>
                      <p className="text-gray-500 text-sm">Last Donation</p>
                      <p className="font-medium text-gray-800">
                        {donorProfile.lastDonation
                          ? new Date(donorProfile.lastDonation).toLocaleDateString()
                          : 'No record'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Stats Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Total Donations */}
                <div className="bg-gradient-to-r from-red-500 to-red-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-red-100 text-sm">Total Donations</p>
                      <p className="text-3xl font-bold">{donorProfile.totalDonations}</p>
                      <p className="text-red-100 text-sm">Times donated</p>
                    </div>
                    <div className="bg-white bg-opacity-20 p-3 rounded-full">
                      <Heart className="h-8 w-8" />
                    </div>
                  </div>
                </div>

                {/* Lives Saved */}
                <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-100 text-sm">Lives Saved</p>
                      <p className="text-3xl font-bold">{donorProfile.livesSaved}</p>
                      <p className="text-green-100 text-sm">People helped</p>
                    </div>
                    <div className="bg-white bg-opacity-20 p-3 rounded-full">
                      <Target className="h-8 w-8" />
                    </div>
                  </div>
                </div>

                {/* Nearby Requests */}
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-100 text-sm">Nearby Requests</p>
                      <p className="text-3xl font-bold">{nearbyRequests.length}</p>
                      <p className="text-blue-100 text-sm">Need your help</p>
                    </div>
                    <div className="bg-white bg-opacity-20 p-3 rounded-full">
                      <Bell className="h-8 w-8" />
                    </div>
                  </div>
                </div>

                {/* Eligibility Status */}
                <div className={`bg-gradient-to-r ${nextEligibleDate?.canDonate ? 'from-green-500 to-green-600' : 'from-yellow-500 to-yellow-600'} p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={`${nextEligibleDate?.canDonate ? 'text-green-100' : 'text-yellow-100'} text-sm`}>Donation Status</p>
                      <p className="text-3xl font-bold">{nextEligibleDate?.canDonate ? 'Ready' : nextEligibleDate?.daysRemaining || 'N/A'}</p>
                      <p className={`${nextEligibleDate?.canDonate ? 'text-green-100' : 'text-yellow-100'} text-sm`}>
                        {nextEligibleDate?.canDonate ? 'Can donate now' : 'Days remaining'}
                      </p>
                    </div>
                    <div className="bg-white bg-opacity-20 p-3 rounded-full">
                      <Clock className="h-8 w-8" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Next Donation Eligibility */}
              {donorProfile.lastDonation && nextEligibleDate && (
                <div className="bg-white rounded-xl shadow-lg p-6 flex items-center space-x-4">
                  <Clock className="h-8 w-8 text-blue-500" />
                  <div>
                    <h2 className="text-xl font-bold text-gray-800">Next Donation Eligibility</h2>
                    {nextEligibleDate.canDonate ? (
                      <p className="text-green-600 font-medium">You are currently eligible to donate!</p>
                    ) : (
                      <p className="text-gray-600">
                        You will be eligible to donate on{' '}
                        <span className="font-medium text-gray-800">{nextEligibleDate.date.toLocaleDateString()}</span>
                        {' '}({nextEligibleDate.daysRemaining} days away)
                      </p>
                    )}
                  </div>
                </div>
              )}

              {/* Who Needs Me? (Blood Type Requests) */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-bold text-gray-800 flex items-center space-x-2">
                    <Target className="h-5 w-5 text-red-500" />
                    {showAllRequests ? `All ${donorProfile.bloodType} Requests` : `Who Needs Me? ${donorProfile.bloodType && `(${donorProfile.bloodType})`}`}
                  </h2>
                  <div className="flex space-x-2">
                    {showAllRequests && (
                      <button
                        onClick={() => setShowAllRequests(false)}
                        className="text-gray-600 hover:underline text-sm font-medium"
                      >
                        Show Summary
                      </button>
                    )}
                    <button
                      onClick={handleNavigateToRequests}
                      className="text-blue-600 hover:underline text-sm font-medium"
                    >
                      {showAllRequests ? 'Refresh' : 'View All Requests'}
                    </button>
                  </div>
                </div>

                {showAllRequests ? (
                  // Show all blood type requests
                  allBloodTypeRequests.length > 0 ? (
                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {allBloodTypeRequests.map(request => (
                        <div key={request.id} className="border border-gray-200 rounded-lg p-4 flex items-center justify-between hover:bg-gray-50 transition-colors">
                          <div>
                            <h3 className="font-bold text-gray-800">{request.hospitalName}</h3>
                            <p className="text-gray-600 text-sm">
                              <MapPin className="h-4 w-4 inline-block mr-1" />
                              {request.location} • Posted {request.timePosted}
                            </p>
                            <p className="text-gray-700 text-sm mt-1">{request.units} units of {request.bloodType} needed</p>
                          </div>
                          <div className="flex items-center space-x-3">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(request.urgency)}`}>
                              {request.urgency} priority
                            </span>
                            <button
                              onClick={() => handleRespondToRequest(request.id)}
                              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                            >
                              Respond
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">No blood requests found for your blood type ({donorProfile.bloodType}) at the moment.</p>
                    </div>
                  )
                ) : (
                  // Show nearby requests summary
                  nearbyRequests.length > 0 ? (
                    <div className="space-y-4">
                      {nearbyRequests.slice(0, 3).map(request => (
                        <div key={request.id} className="border border-gray-200 rounded-lg p-4 flex items-center justify-between">
                          <div>
                            <h3 className="font-bold text-gray-800">{request.hospitalName}</h3>
                            <p className="text-gray-600 text-sm">
                              <MapPin className="h-4 w-4 inline-block mr-1" />
                              {request.location} • {request.distance} • Posted {request.timePosted}
                            </p>
                            <p className="text-gray-700 text-sm mt-1">{request.units} units of {request.bloodType} needed</p>
                          </div>
                          <div className="flex items-center space-x-3">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(request.urgency)}`}>
                              {request.urgency} priority
                            </span>
                            <button
                              onClick={() => handleRespondToRequest(request.id)}
                              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                            >
                              Respond
                            </button>
                          </div>
                        </div>
                      ))}
                      {nearbyRequests.length > 3 && (
                        <div className="text-center pt-2">
                          <p className="text-gray-500 text-sm">Showing 3 of {nearbyRequests.length} nearby requests</p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">No nearby blood requests matching your blood type at the moment.</p>
                    </div>
                  )
                )}
              </div>
            </div>
          )}

          {/* Other tabs content */}
          {activeTab === 'requests' && (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-gray-800 flex items-center space-x-2">
                  <Activity className="h-5 w-5 text-red-500" />
                  Blood Requests {donorProfile.bloodType && `(${donorProfile.bloodType})`}
                </h2>
                <button
                  onClick={handleNavigateToRequests}
                  className="text-blue-600 hover:underline text-sm font-medium"
                >
                  Refresh
                </button>
              </div>

              {allBloodTypeRequests.length > 0 ? (
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {allBloodTypeRequests.map(request => (
                    <div key={request.id} className="border border-gray-200 rounded-lg p-4 flex items-center justify-between hover:bg-gray-50 transition-colors">
                      <div>
                        <h3 className="font-bold text-gray-800">{request.hospitalName}</h3>
                        <p className="text-gray-600 text-sm">
                          <MapPin className="h-4 w-4 inline-block mr-1" />
                          {request.location} • Posted {request.timePosted}
                        </p>
                        <p className="text-gray-700 text-sm mt-1">{request.units} units of {request.bloodType} needed</p>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(request.urgency)}`}>
                          {request.urgency} priority
                        </span>
                        <button
                          onClick={() => handleRespondToRequest(request.id)}
                          className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                        >
                          Respond
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">
                    {donorProfile.bloodType
                      ? `No blood requests found for your blood type (${donorProfile.bloodType}) at the moment.`
                      : 'Please update your blood type in your profile to see matching requests'
                    }
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'history' && (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Activity className="h-5 w-5 text-teal-500" />
                My Responses & Donations
              </h2>
              {donationHistory.length > 0 ? (
                <div className="space-y-4">
                  {donationHistory.map(donation => (
                    <div key={donation.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium text-gray-800">{donation.bloodType} ({donation.units} units)</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          donation.status === 'completed' || donation.status === 'fulfilled'
                            ? 'bg-green-100 text-green-800'
                            : donation.status === 'pending'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {donation.status === 'fulfilled' ? 'completed' : donation.status}
                        </span>
                      </div>
                      <p className="text-gray-600 text-sm">
                        {donation.status === 'fulfilled' || donation.status === 'completed'
                          ? `Donated at ${donation.hospital} on ${new Date(donation.date).toLocaleDateString()}`
                          : `Responded to ${donation.hospital} on ${new Date(donation.date).toLocaleDateString()}`
                        }
                      </p>
                      {donation.location && (
                        <p className="text-gray-500 text-xs mt-1">
                          <MapPin className="h-3 w-3 inline-block mr-1" />
                          {typeof donation.location === 'string' ? donation.location : donation.location.address}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No responses yet. When you respond to blood requests, they will appear here!</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'achievements' && (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Award className="h-5 w-5 text-yellow-500" />
                Achievements
              </h2>
              {donorProfile.achievements.length > 0 ? (
                <div className="space-y-3">
                  {donorProfile.achievements.map((achievement, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <Star className="h-5 w-5 text-yellow-400 fill-current" />
                      <span className="text-gray-700">{achievement}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-gray-600">Make your first donation to unlock achievements!</div>
              )}
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="space-y-6">
              {/* Notification Settings */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center space-x-2">
                  <Bell className="h-5 w-5 text-blue-500" />
                  Notification Preferences
                </h2>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-800">Email Notifications</h3>
                      <p className="text-sm text-gray-600">Receive blood request alerts via email</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.notifications.email}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          notifications: { ...prev.notifications, email: e.target.checked }
                        }))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-800">SMS Notifications</h3>
                      <p className="text-sm text-gray-600">Receive urgent alerts via SMS</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.notifications.sms}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          notifications: { ...prev.notifications, sms: e.target.checked }
                        }))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-800">Push Notifications</h3>
                      <p className="text-sm text-gray-600">Receive browser notifications</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.notifications.push}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          notifications: { ...prev.notifications, push: e.target.checked }
                        }))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>
              </div>

              {/* Privacy Settings */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center space-x-2">
                  <User className="h-5 w-5 text-green-500" />
                  Privacy Settings
                </h2>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-800">Show Location</h3>
                      <p className="text-sm text-gray-600">Allow hospitals to see your general location</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.privacy.showLocation}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          privacy: { ...prev.privacy, showLocation: e.target.checked }
                        }))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-800">Show Last Donation</h3>
                      <p className="text-sm text-gray-600">Display your last donation date to hospitals</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.privacy.showLastDonation}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          privacy: { ...prev.privacy, showLastDonation: e.target.checked }
                        }))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                    </label>
                  </div>
                </div>
              </div>

              {/* Save Button */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <button
                  onClick={() => {
                    toast.success('Settings saved successfully!');
                  }}
                  className="w-full bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 transition-colors font-medium"
                >
                  Save Settings
                </button>
              </div>
            </div>
          )}

          {activeTab === 'profile' && (
            <div className="space-y-6">
              {/* Profile Information */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold text-gray-800 flex items-center space-x-2">
                    <User className="h-5 w-5 text-blue-500" />
                    Profile Information
                  </h2>
                  <button
                    onClick={() => {
                      setEditProfileData({
                        name: donorProfile.name,
                        email: user?.email || '',
                        phone: donorProfile.phone || '',
                        bloodType: donorProfile.bloodType || '',
                        location: donorProfile.location || { address: '', coordinates: { latitude: 0, longitude: 0 } }
                      });
                      setShowEditProfile(true);
                    }}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Edit Profile
                  </button>
                </div>

                {!showEditProfile ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                      <p className="text-gray-900 bg-gray-50 p-3 rounded-lg">{donorProfile.name || 'Not specified'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                      <p className="text-gray-900 bg-gray-50 p-3 rounded-lg">{user?.email || 'Not specified'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                      <p className="text-gray-900 bg-gray-50 p-3 rounded-lg">{donorProfile.phone || 'Not specified'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Blood Type</label>
                      <p className="bg-gray-50 p-3 rounded-lg font-medium text-red-600">
                        {donorProfile.bloodType || 'Not specified'}
                      </p>
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                      <p className="text-gray-900 bg-gray-50 p-3 rounded-lg">
                        {donorProfile.location?.address || 'Not specified'}
                      </p>
                    </div>
                  </div>
                ) : (
                  <form onSubmit={async (e) => {
                    e.preventDefault();
                    try {
                      const response = await authAPI.updateProfile(editProfileData);
                      if (response.data.success) {
                        setDonorProfile(prev => ({ ...prev, ...editProfileData }));
                        setShowEditProfile(false);
                        toast.success('Profile updated successfully!');
                      } else {
                        toast.error('Failed to update profile');
                      }
                    } catch (error) {
                      console.error('Error updating profile:', error);
                      toast.error('Error updating profile');
                    }
                  }}>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                        <input
                          type="text"
                          value={editProfileData.name}
                          onChange={(e) => setEditProfileData(prev => ({ ...prev, name: e.target.value }))}
                          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input
                          type="email"
                          value={editProfileData.email}
                          onChange={(e) => setEditProfileData(prev => ({ ...prev, email: e.target.value }))}
                          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                        <input
                          type="tel"
                          value={editProfileData.phone}
                          onChange={(e) => setEditProfileData(prev => ({ ...prev, phone: e.target.value }))}
                          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Blood Type</label>
                        <select
                          value={editProfileData.bloodType}
                          onChange={(e) => setEditProfileData(prev => ({ ...prev, bloodType: e.target.value }))}
                          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          required
                        >
                          <option value="">Select Blood Type</option>
                          {['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'].map(type => (
                            <option key={type} value={type}>{type}</option>
                          ))}
                        </select>
                      </div>
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                        <input
                          type="text"
                          value={editProfileData.location?.address || ''}
                          onChange={(e) => setEditProfileData(prev => ({
                            ...prev,
                            location: { ...prev.location, address: e.target.value }
                          }))}
                          placeholder="Enter your city or address"
                          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                    </div>

                    <div className="flex space-x-4 mt-6">
                      <button
                        type="submit"
                        className="flex-1 bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium"
                      >
                        Save Changes
                      </button>
                      <button
                        type="button"
                        onClick={() => setShowEditProfile(false)}
                        className="flex-1 bg-gray-600 text-white py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors font-medium"
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                )}
              </div>

              {/* Account Statistics */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center space-x-2">
                  <Activity className="h-5 w-5 text-green-500" />
                  Account Statistics
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="bg-red-100 p-4 rounded-full w-16 h-16 mx-auto mb-2 flex items-center justify-center">
                      <Heart className="h-8 w-8 text-red-600" />
                    </div>
                    <p className="text-2xl font-bold text-gray-800">{donorProfile.totalDonations}</p>
                    <p className="text-sm text-gray-600">Total Donations</p>
                  </div>
                  <div className="text-center">
                    <div className="bg-green-100 p-4 rounded-full w-16 h-16 mx-auto mb-2 flex items-center justify-center">
                      <Target className="h-8 w-8 text-green-600" />
                    </div>
                    <p className="text-2xl font-bold text-gray-800">{donorProfile.livesSaved}</p>
                    <p className="text-sm text-gray-600">Lives Saved</p>
                  </div>
                  <div className="text-center">
                    <div className="bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-2 flex items-center justify-center">
                      <Calendar className="h-8 w-8 text-blue-600" />
                    </div>
                    <p className="text-2xl font-bold text-gray-800">
                      {donorProfile.lastDonation
                        ? new Date(donorProfile.lastDonation).toLocaleDateString()
                        : 'Never'
                      }
                    </p>
                    <p className="text-sm text-gray-600">Last Donation</p>
                  </div>
                </div>
              </div>

              {/* Account Actions */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center space-x-2">
                  <Settings className="h-5 w-5 text-purple-500" />
                  Account Actions
                </h2>
                <div className="space-y-3">
                  <button
                    onClick={() => {
                      if (window.confirm('Are you sure you want to reset your password? You will receive an email with instructions.')) {
                        toast.info('Password reset email sent!');
                      }
                    }}
                    className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="bg-blue-100 p-2 rounded-full">
                        <User className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-800">Reset Password</h3>
                        <p className="text-sm text-gray-600">Change your account password</p>
                      </div>
                    </div>
                  </button>

                  <button
                    onClick={() => {
                      if (window.confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
                        toast.error('Account deletion is not available yet. Please contact support.');
                      }
                    }}
                    className="w-full text-left p-4 border border-red-200 rounded-lg hover:bg-red-50 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="bg-red-100 p-2 rounded-full">
                        <User className="h-4 w-4 text-red-600" />
                      </div>
                      <div>
                        <h3 className="font-medium text-red-800">Delete Account</h3>
                        <p className="text-sm text-red-600">Permanently delete your account</p>
                      </div>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default DonorDashboard;
