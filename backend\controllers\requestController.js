const BloodRequest = require('../models/BloodRequest');
const User = require('../models/User');
const BloodInventory = require('../models/BloodInventory');
const { isCompatible, getCompatibleDonors } = require('../utils/bloodTypeUtils');
const notificationService = require('../services/notificationService');

// Create a new blood request
const createRequest = async (req, res) => {
  try {
    const { bloodType, units, urgency, location, notes, description } = req.body;
    const hospitalId = req.user.id;

    // Check if hospital has sufficient inventory
    const inventory = await BloodInventory.findOne({
      hospital: hospitalId,
      bloodType,
      status: 'available'
    });

    if (inventory && inventory.units >= units) {
      return res.status(400).json({
        success: false,
        message: 'Sufficient blood inventory available. No need to create request.'
      });
    }

    const request = new BloodRequest({
      hospital: hospitalId,
      bloodType,
      units,
      urgency,
      location,
      notes,
      description,
      status: 'pending'
    });

    await request.save();

    // Find compatible donors (removed coordinate-based search here)
    // const compatibleTypes = getCompatibleDonors(bloodType);
    // const matchedDonors = await User.find({
    //   bloodType: { $in: compatibleTypes },
    //   role: 'donor',
    //   'location.coordinates': {
    //     $near: {
    //       $geometry: {
    //         type: 'Point',
    //         coordinates: [location.coordinates.longitude, location.coordinates.latitude]
    //       },
    //       $maxDistance: 50000 // 50km radius
    //     }
    //   }
    // }).select('_id');

    // Notify matched donors (adjust if donor matching is done elsewhere)
    // await notificationService.notifyMatchedDonors(request, matchedDonors.map(d => d._id));

    res.status(201).json({
      success: true,
      request,
      // matchedDonors: matchedDonors.length // Adjust response if needed
    });
  } catch (error) {
    console.error('Error creating request:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

// Get nearby blood requests
const getNearbyRequests = async (req, res) => {
  try {
    const { latitude, longitude, maxDistance = 50000 } = req.query;
    const donorId = req.user.id;

    const donor = await User.findById(donorId);
    if (!donor) {
      return res.status(404).json({ success: false, message: 'Donor not found' });
    }

    const requests = await BloodRequest.find({
      status: 'pending',
      'location.coordinates': {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [longitude, latitude]
          },
          $maxDistance: parseInt(maxDistance)
        }
      }
    })
    .populate('hospital', 'name location')
    .sort({ urgency: -1, createdAt: -1 });

    // Filter requests based on blood type compatibility
    const compatibleRequests = requests.filter(request =>
      isCompatible(donor.bloodType, request.bloodType)
    );

    res.json({
      success: true,
      requests: compatibleRequests
    });
  } catch (error) {
    console.error('Error getting nearby requests:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

// Get all requests (for admin)
const getAllRequests = async (req, res) => {
  try {
    const requests = await BloodRequest.find()
      .populate('hospital', 'name location')
      .populate('fulfilledBy', 'name bloodType')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      requests
    });
  } catch (error) {
    console.error('Error getting all requests:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

// Get requests by blood type (for donors)
const getRequestsByBloodType = async (req, res) => {
  try {
    const { bloodType } = req.query;
    const donorId = req.user.id;

    if (!bloodType) {
      return res.status(400).json({ success: false, message: 'Blood type is required' });
    }

    const donor = await User.findById(donorId);
    if (!donor) {
      return res.status(404).json({ success: false, message: 'Donor not found' });
    }

    // Find all pending requests that match the donor's blood type
    const requests = await BloodRequest.find({
      status: 'pending',
      bloodType: bloodType
    })
    .populate('hospital', 'name location')
    .sort({ urgency: -1, createdAt: -1 });

    // Filter requests based on blood type compatibility
    const compatibleRequests = requests.filter(request =>
      isCompatible(donor.bloodType, request.bloodType)
    );

    res.json({
      success: true,
      requests: compatibleRequests
    });
  } catch (error) {
    console.error('Error getting requests by blood type:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

// Fulfill a blood request
const fulfillRequest = async (req, res) => {
  try {
    const { id } = req.params;
    const donorId = req.user.id;

    const request = await BloodRequest.findById(id);
    if (!request) {
      return res.status(404).json({ success: false, message: 'Request not found' });
    }

    if (request.status !== 'pending') {
      return res.status(400).json({ success: false, message: 'Request is no longer active' });
    }

    const donor = await User.findById(donorId);
    if (!isCompatible(donor.bloodType, request.bloodType)) {
      return res.status(400).json({ success: false, message: 'Blood type not compatible' });
    }

    // Update request status
    request.status = 'fulfilled';
    request.fulfilledBy = donorId;
    request.fulfilledAt = new Date();
    await request.save();

    // Update blood inventory
    const inventory = await BloodInventory.findOne({
      hospital: request.hospital,
      bloodType: request.bloodType
    });

    if (inventory) {
      inventory.units += request.units;
      await inventory.save();
    } else {
      await new BloodInventory({
        hospital: request.hospital,
        bloodType: request.bloodType,
        units: request.units,
        expiryDate: new Date(Date.now() + 42 * 24 * 60 * 60 * 1000) // 42 days from now
      }).save();
    }

    // Update donor's last donation date
    donor.lastDonation = new Date();
    await donor.save();

    // Send notifications
    await notificationService.notifyRequestFulfilled(request, donor);

    res.json({
      success: true,
      message: 'Request fulfilled successfully',
      request
    });
  } catch (error) {
    console.error('Error fulfilling request:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

// Get hospital-specific requests
const getHospitalRequests = async (req, res) => {
  try {
    const hospitalId = req.user.id;

    const requests = await BloodRequest.find({ hospital: hospitalId })
      .populate('fulfilledBy', 'name bloodType')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      requests
    });
  } catch (error) {
    console.error('Error getting hospital requests:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

// Get matched donors for a request
const getMatchedDonors = async (req, res) => {
  try {
    const { id } = req.params;
    const request = await BloodRequest.findById(id);

    if (!request) {
      return res.status(404).json({ success: false, message: 'Request not found' });
    }

    const compatibleTypes = getCompatibleDonors(request.bloodType);
    const matchedDonors = await User.find({
      bloodType: { $in: compatibleTypes },
      role: 'donor',
      'location.coordinates': {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [
              request.location.coordinates.longitude,
              request.location.coordinates.latitude
            ]
          },
          $maxDistance: 50000 // 50km radius
        }
      }
    }).select('name bloodType location lastDonation');

    res.json({
      success: true,
      matchedDonors
    });
  } catch (error) {
    console.error('Error getting matched donors:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

module.exports = {
  createRequest,
  getNearbyRequests,
  getAllRequests,
  getRequestsByBloodType,
  fulfillRequest,
  getHospitalRequests,
  getMatchedDonors
};
