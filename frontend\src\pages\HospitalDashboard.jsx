import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { authAPI, requestAPI } from '../services/api';
import { toast } from 'react-hot-toast';
import {
  Building,
  Heart,
  Users,
  Activity,
  Plus,
  Clock,
  CheckCircle,
  AlertCircle,
  MapPin,
  Phone,
  Mail,
  Edit,
  Send,
  Calendar,
  TrendingUp,
  Shield,
  Target,
  Bell,
  Loader2,
  Menu,
  X,
  LogOut,
  User,
  FileText,
  Home,
  ChevronRight,
  Settings
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const HospitalDashboard = () => {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [activeRequests, setActiveRequests] = useState([]);
  const [matchedDonors, setMatchedDonors] = useState([]);
  const [hospitalStats, setHospitalStats] = useState({
    activeRequests: 0,
    matchedDonors: 0,
    fulfilledRequests: 0,
    avgResponseTime: '0h'
  });
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showMatchedDonorsModal, setShowMatchedDonorsModal] = useState(false);
  const [selectedRequestForDonors, setSelectedRequestForDonors] = useState(null);

  // Real hospital profile data
  const [hospitalProfile, setHospitalProfile] = useState({
    name: user?.name || '',
    email: '',
    location: null,
    phone: '',
    address: '',
    verified: false
  });

  const navigate = useNavigate();

  // State for emergency alert modal
  const [showEmergencyAlertModal, setShowEmergencyAlertModal] = useState(false);
  const [alertData, setAlertData] = useState({
    bloodType: '',
    message: '',
    // Location will be derived from the hospital user's profile on the backend
  });

  // Fetch real hospital profile data
  useEffect(() => {
    const fetchHospitalProfile = async () => {
      try {
        if (user) {
          const response = await authAPI.getProfile();
          if (response.data.success && response.data.user && response.data.user.role === 'hospital') {
            setHospitalProfile(response.data.user);
          } else {
            toast.error('Failed to fetch hospital profile');
          }
        }
      } catch (error) {
        console.error('Error fetching hospital profile:', error);
        toast.error('Error fetching hospital profile');
      }
    };

    fetchHospitalProfile();
  }, [user]);

  // Fetch hospital's blood requests and calculate stats
  useEffect(() => {
    const fetchHospitalRequests = async () => {
      try {
        const response = await requestAPI.getHospitalRequests();

        if (response.data.success) {
          const requests = response.data.requests;

          // Filter active (pending) requests
          const pendingRequests = requests.filter(req => req.status === 'pending');
          setActiveRequests(pendingRequests);

          // Calculate stats from fetched data
          const activeCount = pendingRequests.length;
          const fulfilledCount = requests.filter(req => req.status === 'fulfilled').length;
          const totalMatchedDonors = pendingRequests.reduce((sum, req) => sum + (req.matchedDonors?.length || 0), 0);
          // TODO: Calculate avgResponseTime if fulfillment dates are available and meaningful for average
          const avgResponseTime = 'N/A'; // Placeholder for now

          setHospitalStats({
            activeRequests: activeCount,
            matchedDonors: totalMatchedDonors,
            fulfilledRequests: fulfilledCount,
            avgResponseTime: avgResponseTime
          });

        } else {
          toast.error('Failed to fetch hospital requests');
          setActiveRequests([]);
        }

      } catch (error) {
        console.error('Error fetching hospital requests:', error);
        toast.error('Error fetching hospital requests');
        setActiveRequests([]);
      } finally {
        setLoading(false);
      }
    };

    fetchHospitalRequests();
  }, []);

  const getUrgencyColor = (urgency) => {
    switch (urgency) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'fulfilled': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleFulfillRequest = async (requestId) => {
    try {
      await requestAPI.fulfillRequest(requestId);
      setActiveRequests(prev =>
        prev.map(req =>
          req.id === requestId
            ? { ...req, status: 'fulfilled' }
            : req
        )
      );
      alert(`Request ${requestId} marked as fulfilled!`);
    } catch (error) {
      console.error('Error fulfilling request:', error);
      alert('Failed to fulfill request. Please try again.');
    }
  };

  const handleContactDonor = (donorId, donorName) => {
    // TODO: Implement contact donor functionality
    alert(`Contacting ${donorName}... Feature coming soon!`);
  };

  const handleViewMatchedDonors = async (request) => {
    setSelectedRequestForDonors(request);
    try {
      const response = await requestAPI.getMatchedDonors(request.id);
      if (response.data.success) {
        setMatchedDonors(response.data.matchedDonors || []);
      } else {
        toast.error('Failed to fetch matched donors for this request');
        setMatchedDonors([]);
      }
    } catch (error) {
      console.error('Error fetching matched donors:', error);
      toast.error('Error fetching matched donors for this request');
      setMatchedDonors([]);
    }
    setShowMatchedDonorsModal(true);
  };

  const handleCreateRequestClick = () => {
    // Assuming the BloodRequests page has the modal/form for creating requests
    navigate('/blood-requests');
  };

  const handleSendEmergencyAlert = async () => {
    console.log('Sending emergency alert:', alertData);
    try {
      if (!hospitalProfile.location || !hospitalProfile.location.coordinates) {
        toast.error('Hospital location is not available in profile.');
        return;
      }

      const locationData = {
        latitude: hospitalProfile.location.coordinates[1],
        longitude: hospitalProfile.location.coordinates[0],
      };

      const response = await alertAPI.sendEmergencyAlert({
        bloodType: alertData.bloodType,
        message: alertData.message,
        location: locationData,
      });

      if (response.data.success) {
        toast.success(response.data.message || 'Emergency alert sent successfully!');
        setAlertData({ bloodType: '', message: '' });
      } else {
        toast.error(response.data.message || 'Failed to send emergency alert.');
      }
    } catch (error) {
      console.error('Error sending emergency alert:', error);
      toast.error('An error occurred while sending the alert.');
    } finally {
      setShowEmergencyAlertModal(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading hospital dashboard...</p>
        </div>
      </div>
    );
  }

  // Calculate success rate
  const totalRequests = hospitalStats.activeRequests + hospitalStats.fulfilledRequests;
  const successRate = totalRequests > 0 ? ((hospitalStats.fulfilledRequests / totalRequests) * 100).toFixed(0) : 0;

  // Sidebar menu items
  const sidebarItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Home, active: true },
    { id: 'requests', label: 'Blood Requests', icon: Activity },
    { id: 'donors', label: 'Browse Donors', icon: Users },
    { id: 'reports', label: 'Reports', icon: FileText },
    { id: 'settings', label: 'Settings', icon: Settings },
    { id: 'profile', label: 'Profile', icon: User }
  ];

  return (
    <div className="min-h-screen bg-gray-100 flex">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-64' : 'w-20'} bg-white shadow-xl transition-all duration-300 ease-in-out flex flex-col`}>
        {/* Sidebar Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="bg-blue-600 p-2 rounded-lg">
              <Building className="h-6 w-6 text-white" />
            </div>
            {sidebarOpen && (
              <div>
                <h2 className="text-lg font-bold text-gray-800">🩸 SomDonate</h2>
                <p className="text-sm text-gray-600">Hospital Panel</p>
              </div>
            )}
          </div>
        </div>

        {/* Sidebar Menu */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {sidebarItems.map((item) => (
              <li key={item.id}>
                <button
                  onClick={() => setActiveTab(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                    activeTab === item.id
                      ? 'bg-blue-50 text-blue-600 border-r-4 border-blue-600'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                  }`}
                >
                  <item.icon className="h-5 w-5" />
                  {sidebarOpen && <span className="font-medium">{item.label}</span>}
                  {sidebarOpen && activeTab === item.id && (
                    <ChevronRight className="h-4 w-4 ml-auto" />
                  )}
                </button>
              </li>
            ))}
          </ul>
        </nav>

        {/* Sidebar Footer */}
        <div className="p-4 border-t border-gray-200">
          <button
            onClick={logout}
            className="w-full flex items-center space-x-3 px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
          >
            <LogOut className="h-5 w-5" />
            {sidebarOpen && <span className="font-medium">Logout</span>}
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <Menu className="h-5 w-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">
                  {sidebarItems.find(item => item.id === activeTab)?.label || 'Dashboard'}
                </h1>
                <p className="text-sm text-gray-600">Welcome back, {user?.name}</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <button className="relative p-2 rounded-lg hover:bg-gray-100 transition-colors">
                <Bell className="h-5 w-5 text-gray-600" />
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  3
                </span>
              </button>

              {/* Profile */}
              <div className="flex items-center space-x-3">
                <div className="bg-blue-600 p-2 rounded-full">
                  <Building className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-800">{user?.name}</p>
                  <p className="text-xs text-gray-600">Hospital</p>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 p-6 bg-gray-50 overflow-auto">
          {/* Dashboard Content */}
          {activeTab === 'dashboard' && (
            <div className="space-y-6">
              {/* Hospital Profile Card */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex justify-between items-start mb-4">
                  <h2 className="text-xl font-bold text-gray-800 flex items-center space-x-2">
                    <Building className="h-5 w-5" />
                    <span>Hospital Profile</span>
                  </h2>
                  <div className="flex items-center space-x-2">
                    {hospitalProfile.verified ? (
                      <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                        <CheckCircle className="h-4 w-4" />
                        <span>Verified</span>
                      </span>
                    ) : (
                      <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                        Pending Verification
                      </span>
                    )}
                    <button className="text-blue-600 hover:text-blue-800">
                      <Edit className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-blue-500" />
                    <div>
                      <p className="text-sm text-gray-600">Email</p>
                      <p className="font-semibold text-gray-800">{hospitalProfile.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <MapPin className="h-5 w-5 text-green-500" />
                    <div>
                      <p className="text-sm text-gray-600">Location</p>
                      <p className="font-semibold text-gray-800">{hospitalProfile.location?.address || 'Not specified'}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-purple-500" />
                    <div>
                      <p className="text-sm text-gray-600">Phone</p>
                      <p className="font-semibold text-gray-800">{hospitalProfile.phone}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Building className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">Address</p>
                      <p className="font-semibold text-gray-800">{hospitalProfile.address}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Stats Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Active Requests */}
                <div className="bg-gradient-to-r from-red-500 to-red-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-red-100 text-sm">Active Requests</p>
                      <p className="text-3xl font-bold">{hospitalStats.activeRequests}</p>
                      <p className="text-red-100 text-sm">Urgent attention needed</p>
                    </div>
                    <div className="bg-white bg-opacity-20 p-3 rounded-full">
                      <Activity className="h-8 w-8" />
                    </div>
                  </div>
                </div>

                {/* Matched Donors */}
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-100 text-sm">Matched Donors</p>
                      <p className="text-3xl font-bold">{hospitalStats.matchedDonors}</p>
                      <p className="text-blue-100 text-sm">Available to contact</p>
                    </div>
                    <div className="bg-white bg-opacity-20 p-3 rounded-full">
                      <Users className="h-8 w-8" />
                    </div>
                  </div>
                </div>

                {/* Fulfilled Requests */}
                <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-100 text-sm">Fulfilled Requests</p>
                      <p className="text-3xl font-bold">{hospitalStats.fulfilledRequests}</p>
                      <p className="text-green-100 text-sm">Lives saved</p>
                    </div>
                    <div className="bg-white bg-opacity-20 p-3 rounded-full">
                      <CheckCircle className="h-8 w-8" />
                    </div>
                  </div>
                </div>

                {/* Success Rate */}
                <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-100 text-sm">Success Rate</p>
                      <p className="text-3xl font-bold">{successRate}%</p>
                      <p className="text-purple-100 text-sm">Getting better</p>
                    </div>
                    <div className="bg-white bg-opacity-20 p-3 rounded-full">
                      <TrendingUp className="h-8 w-8" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Active Blood Requests */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold text-gray-800 flex items-center space-x-2">
                    <Activity className="h-5 w-5" />
                    <span>Active Blood Requests</span>
                  </h2>
                  {hospitalProfile.verified ? (
                    <button
                      onClick={handleCreateRequestClick}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                    >
                      <Plus className="h-5 w-5" />
                      <span>Create Request</span>
                    </button>
                  ) : (
                    <span className="text-gray-500 text-sm">Verify your hospital profile to create requests.</span>
                  )}
                </div>

                {activeRequests.length > 0 ? (
                  <div className="space-y-4">
                    {activeRequests.map(request => (
                      <div key={request.id} className="border border-gray-200 rounded-lg p-4 flex justify-between items-center">
                        <div>
                          <h3 className="font-bold text-gray-800">{request.bloodType} ({request.units} units)</h3>
                          <p className="text-gray-600 text-sm mt-1">Urgency: <span className={`px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(request.urgency)}`}>{request.urgency}</span></p>
                          <p className="text-gray-600 text-sm">Posted: {new Date(request.datePosted).toLocaleString()}</p>
                        </div>
                        <div className="flex space-x-3">
                          <button
                            onClick={() => handleViewMatchedDonors(request)}
                            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                          >
                            Matched Donors ({request.matchedDonors || 0})
                          </button>
                          <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm">
                            Edit
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Heart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-600">No active blood requests.</p>
                    {hospitalProfile.verified && (
                      <button onClick={handleCreateRequestClick} className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">Create Your First Request</button>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Other tabs content will be added here */}
          {activeTab === 'requests' && (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-4">Blood Requests Management</h2>
              <p className="text-gray-600">Manage all your blood requests here.</p>
              <button
                onClick={() => navigate('/blood-requests')}
                className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Go to Blood Requests
              </button>
            </div>
          )}

          {activeTab === 'donors' && (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-4">Browse Donors</h2>
              <p className="text-gray-600">Find and contact potential blood donors.</p>
              <button
                onClick={() => navigate('/browse-donors')}
                className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Browse Donors
              </button>
            </div>
          )}

          {activeTab === 'reports' && (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-4">Reports & Analytics</h2>
              <p className="text-gray-600">View detailed reports and analytics.</p>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-4">Settings</h2>
              <p className="text-gray-600">Manage your hospital settings.</p>
            </div>
          )}

          {activeTab === 'profile' && (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-4">Profile Management</h2>
              <p className="text-gray-600">Update your hospital profile information.</p>
              <button
                onClick={() => navigate('/profile')}
                className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Edit Profile
              </button>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default HospitalDashboard;
